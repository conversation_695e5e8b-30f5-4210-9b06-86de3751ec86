{"expo": {"name": "BusBeat Pilot", "slug": "busdriver", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/splash-icon-light.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "icon": {"light": "./assets/images/ios-light.png"}, "infoPlist": {"UIBackgroundModes": ["location", "fetch", "location", "fetch", "location", "fetch", "location", "fetch", "remote-notification"], "NSLocationWhenInUseUsageDescription": "This app needs access to your location to track the bus route.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs access to your location in the background to continue tracking the bus route even when the app is not in use.", "ITSAppUsesNonExemptEncryption": false}, "bundleIdentifier": "com.blackboytechie.busdriver"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#000000"}, "package": "com.fantom_atom.driverapp", "permissions": ["ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION", "ACCESS_BACKGROUND_LOCATION", "FOREGROUND_SERVICE", "WAKE_LOCK", "RECEIVE_BOOT_COMPLETED", "VIBRATE", "BLUETOOTH", "BLUETOOTH_ADMIN", "BLUETOOTH_SCAN", "BLUETOOTH_CONNECT", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_BACKGROUND_LOCATION", "android.permission.FOREGROUND_SERVICE", "android.permission.FOREGROUND_SERVICE_LOCATION", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.VIBRATE", "android.permission.POST_NOTIFICATIONS", "android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_SCAN", "android.permission.BLUETOOTH_CONNECT"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon-light.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#FFFFFF"}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location to track the bus route in the background.", "locationAlwaysPermission": "Allow $(PRODUCT_NAME) to use your location to track the bus route in the background.", "locationWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location to track the bus route.", "isIosBackgroundLocationEnabled": true, "isAndroidBackgroundLocationEnabled": true}], ["expo-notifications", {"icon": "./assets/images/icon.png", "color": "#ffffff", "sounds": []}], ["expo-build-properties", {"android": {"compileSdkVersion": 34, "targetSdkVersion": 34, "buildToolsVersion": "34.0.0"}}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "5760642e-bed6-4fc1-9d6d-c86e10d69663"}}, "owner": "blackboy<PERSON>ie", "updates": {"enabled": true, "fallbackToCacheTimeout": 0, "checkAutomatically": "ON_LOAD", "url": "https://u.expo.dev/5760642e-bed6-4fc1-9d6d-c86e10d69663"}, "runtimeVersion": "exposdk:52.0.0"}}