{"name": "driver-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.758.0", "@aws-sdk/lib-dynamodb": "^3.758.0", "@expo/fingerprint": "^0.12.4", "@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "aws-sdk": "^2.1692.0", "axios": "^1.8.3", "depcheck": "^1.4.7", "expo": "~52.0.38", "expo-background-fetch": "~13.0.5", "expo-battery": "~9.0.2", "expo-blur": "~14.0.3", "expo-constants": "~17.0.7", "expo-dev-client": "~5.0.14", "expo-device": "~7.0.2", "expo-file-system": "^18.1.10", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-keep-awake": "^14.0.3", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-location": "~18.0.8", "expo-media-library": "^17.1.7", "expo-notifications": "~0.29.14", "expo-print": "^14.1.4", "expo-router": "~4.0.19", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.8", "expo-task-manager": "~12.0.5", "expo-updates": "~0.27.4", "expo-web-browser": "~14.0.2", "i18next": "^24.2.3", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^15.4.1", "react-native": "0.76.7", "react-native-ble-plx": "^3.5.0", "react-native-dotenv": "^3.4.11", "react-native-event-listeners": "^1.0.7", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^18.0.0", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}